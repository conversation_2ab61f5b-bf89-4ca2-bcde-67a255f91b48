# 环境变量配置示例
# 复制此文件为 .env 并根据实际情况修改配置值

# 应用配置
NODE_ENV=development
PORT=51080

# 数据库配置 - SQL Server
SQLSERVER_HOST=*************
SQLSERVER_PORT=1433
SQLSERVER_USER=sa
SQLSERVER_PASSWORD=123456
SQLSERVER_DB=NBSTEST
SQLSERVER_ENCRYPT=false
SQLSERVER_TRUST_CERT=true

# 数据库类型选择
DATABASE_TYPE=sqlserver

# 会话配置
SESSION_SECRET=your-secret-key-here

# 天地图API配置
# 申请地址：https://console.tianditu.gov.cn/
TIANDITU_KEY=your-tianditu-api-key-here
TIANDITU_GEOCODER_URL=http://api.tianditu.gov.cn/geocoder
TIANDITU_TIMEOUT=10000
TIANDITU_RETRY_ATTEMPTS=3
TIANDITU_RETRY_DELAY=1000

# 日志配置
LOG_LEVEL=warn
LOG_ENABLE_CONSOLE=false
