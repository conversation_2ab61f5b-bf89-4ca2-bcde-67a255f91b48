<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <system.webServer>

    <!-- 移除可能干扰的模块 -->
    <modules>
      <remove name="WebDAVModule" />
    </modules>

    <!-- 处理程序配置 - 支持所有HTTP动词包括DELETE请求 -->
    <handlers>
      <remove name="WebDAV" />
      <add name="iisnode" path="*.js" verb="*" modules="iisnode" resourceType="Unspecified" requireAccess="Script" />
    </handlers>

    <!-- 静态文件MIME类型配置 -->
    <staticContent>
      <!-- 移除可能存在的重复配置 -->
      <remove fileExtension=".jpg" />
      <remove fileExtension=".jpeg" />
      <remove fileExtension=".png" />
      <remove fileExtension=".gif" />
      <remove fileExtension=".webp" />
      <remove fileExtension=".mp4" />
      <remove fileExtension=".webm" />
      <remove fileExtension=".mp3" />

      <!-- 添加MIME类型映射 -->
      <mimeMap fileExtension=".jpg" mimeType="image/jpg" />
      <mimeMap fileExtension=".jpeg" mimeType="image/jpeg" />
      <mimeMap fileExtension=".png" mimeType="image/png" />
      <mimeMap fileExtension=".gif" mimeType="image/gif" />
      <mimeMap fileExtension=".webp" mimeType="image/webp" />
      <mimeMap fileExtension=".mp4" mimeType="video/mp4" />
      <mimeMap fileExtension=".webm" mimeType="video/webm" />
      <mimeMap fileExtension=".mp3" mimeType="audio/mpeg" />
    </staticContent>

    <!-- 确保支持所有HTTP动词和大文件 -->
    <security>
      <requestFiltering>
        <verbs>
          <add verb="GET" allowed="true" />
          <add verb="POST" allowed="true" />
          <add verb="PUT" allowed="true" />
          <add verb="DELETE" allowed="true" />
          <add verb="OPTIONS" allowed="true" />
        </verbs>
        <!-- 允许大文件上传和访问 -->
        <requestLimits maxAllowedContentLength="*********" />
      </requestFiltering>
    </security>

    <!-- URL重写规则 -->
    <rewrite>
      <rules>
        <rule name="NodeInspector" patternSyntax="ECMAScript" stopProcessing="true">
          <match url="^app.js\/debug[\/]?" />
        </rule>
        <!-- 上传文件直接访问规则 -->
        <rule name="UploadFiles" stopProcessing="true">
          <match url="^upload\/.*" />
          <conditions>
            <add input="{REQUEST_FILENAME}" matchType="IsFile" />
          </conditions>
          <action type="None" />
        </rule>
        <rule name="StaticContent">
          <conditions>
            <add input="{REQUEST_FILENAME}" matchType="IsFile" />
          </conditions>
          <action type="None" />
        </rule>
        <rule name="DynamicContent">
          <conditions>
            <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="True" />
            <add input="{REQUEST_METHOD}" pattern="GET|POST|PUT|DELETE|OPTIONS" />
          </conditions>
          <match url="(.*)" />
          <action type="Rewrite" url="app.js" />
        </rule>
      </rules>
    </rewrite>

    <!-- iisnode基础配置 -->
    <iisnode
      loggingEnabled="False"
      debuggingEnabled="false"
      devErrorsEnabled="false"
      nodeProcessCommandLine="node --no-deprecation"
      watchedFiles="web.config;*.js" />
    <!-- 强制重启标记: 2025-07-24-01:38 修复数据库索引问题和认证中间件 -->

  </system.webServer>
</configuration>