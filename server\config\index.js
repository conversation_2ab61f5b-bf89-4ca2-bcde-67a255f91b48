/**
 * 统一配置管理系统
 * 支持不同环境的配置切换，提供配置验证和默认值
 */

const path = require('path');
const fs = require('fs');

/**
 * 配置管理类
 */
class ConfigManager {
  constructor() {
    this.env = process.env.NODE_ENV || 'development';
    this.configs = {};
    this.loadConfigs();
  }

  /**
   * 加载配置文件
   */
  loadConfigs() {
    // 基础配置
    const baseConfig = this.loadBaseConfig();
    
    // 环境特定配置
    const envConfig = this.loadEnvConfig(this.env);
    
    // 本地配置（可选，用于本地开发覆盖）
    const localConfig = this.loadLocalConfig();

    // 合并配置，优先级：local > env > base
    this.configs = {
      ...baseConfig,
      ...envConfig,
      ...localConfig
    };

    // 验证必要配置
    this.validateConfig();
  }

  /**
   * 加载基础配置
   */
  loadBaseConfig() {
    return {
      // 应用配置
      app: {
        name: '现场信息反馈系统',
        version: '1.0.0',
        port: 8081,
        host: '0.0.0.0'
      },

      // SQL Server数据库配置
      sqlserver: {
        server: process.env.SQLSERVER_HOST || '*************',
        port: parseInt(process.env.SQLSERVER_PORT) || 1433,
        user: process.env.SQLSERVER_USER || 'sa',
        password: process.env.SQLSERVER_PASSWORD || '123456',
        database: process.env.SQLSERVER_DB || 'NBSTEST',
        encrypt: process.env.SQLSERVER_ENCRYPT === 'true' || false,
        trustServerCertificate: process.env.SQLSERVER_TRUST_CERT === 'true' || true,
        requestTimeout: 30000,
        connectionTimeout: 30000,
        poolMax: 10,
        poolMin: 0,
        idleTimeout: 30000,
        acquireTimeout: 60000
      },

      // 数据库类型选择：'mysql' 或 'sqlserver'
      databaseType: 'sqlserver', // 当前使用SQL Server

      // 会话配置
      session: {
        secret: 'feedback-management-secret-key',
        name: 'feedback.sid',
        resave: false,
        saveUninitialized: false,
        cookie: {
          secure: false,
          httpOnly: true,
          maxAge: 24 * 60 * 60 * 1000 // 24小时
        }
      },

      // 文件上传配置
      upload: {
        maxFileSize: 100 * 1024 * 1024, // 100MB
        allowedImageTypes: ['jpg', 'jpeg', 'png', 'gif', 'webp'],
        allowedVideoTypes: ['mp4', 'avi', 'mov', 'wmv', 'flv'],
        allowedDocumentTypes: ['pdf', 'doc', 'docx', 'xls', 'xlsx'],
        uploadDir: 'upload',
        imageDir: 'upload/images',
        videoDir: 'upload/videos',
        documentDir: 'upload/documents'
      },

      // 日志配置 - 完全禁用文件日志以节省内存
      logging: {
        level: 'warn', // 只记录警告和错误级别的日志
        maxFiles: 7,   // 减少保留文件数量
        maxSize: '5m', // 减少单个文件大小
        datePattern: 'YYYY-MM-DD',
        enableConsole: true,  // 启用控制台输出
        enableFileLogging: false // 完全禁用文件日志记录
      },

      // 安全配置
      security: {
        bcryptRounds: 10,
        tokenExpiry: 24 * 60 * 60 * 1000, // 24小时
        maxLoginAttempts: 5,
        lockoutTime: 15 * 60 * 1000 // 15分钟
      },

      // CORS配置
      cors: {
        origin: true,
        credentials: true,
        methods: ['GET', 'POST', 'PUT', 'OPTIONS'],
        allowedHeaders: ['Content-Type', 'Authorization', 'X-Current-Company']
      },

      // 分页配置
      pagination: {
        defaultPageSize: 10,
        maxPageSize: 100
      },

      // 视频时长控制配置 - 统一配置入口
      videoDuration: {
        minDuration: 0,    // 最小时长（秒）
        maxDuration: 10,    // 最大时长（秒）
        // 说明：修改此处的值会影响整个系统的视频时长限制
        // 包括：相机录制、视频上传验证、前端显示等
        description: '视频时长控制的统一配置，修改此处会影响整个系统'
      },

      // 业务配置
      business: {
        // 供应状态 - 根据X_ppProduceOrder表的X_SupplyState字段
        supplyStatus: {
          PENDING: 0,      // 待供
          SUPPLYING: 1,    // 正供
          COMPLETED: 2,    // 供毕
          CANCELLED: 3     // 作废
        },

        // 供应状态文本映射
        supplyStatusText: {
          0: '待供',
          1: '正供',
          2: '供毕',
          3: '作废'
        },

        // 供应状态样式类映射
        supplyStatusClass: {
          0: 'pending',
          1: 'supplying',
          2: 'completed',
          3: 'cancelled'
        }
      },

      // 天地图API配置
      tianditu: {
        key: process.env.TIANDITU_KEY || '820b16b66af3ee9aca06f284acf8bd30',
        geocoderUrl: process.env.TIANDITU_GEOCODER_URL || 'http://api.tianditu.gov.cn/geocoder',
        timeout: parseInt(process.env.TIANDITU_TIMEOUT) || 10000,
        retryAttempts: parseInt(process.env.TIANDITU_RETRY_ATTEMPTS) || 3,
        retryDelay: parseInt(process.env.TIANDITU_RETRY_DELAY) || 1000
      }
    };
  }

  /**
   * 加载环境特定配置
   */
  loadEnvConfig(env) {
    const envConfigs = {
      development: {
        app: {
          port: 8081
        },
        database: {
          host: '*************',
          user: 'root',
          password: '123456',
          database: 'feedback_management'
        },
        sqlserver: {
        server: process.env.SQLSERVER_HOST || '*************',
        port: parseInt(process.env.SQLSERVER_PORT) || 1433,
        user: process.env.SQLSERVER_USER || 'sa',
        password: process.env.SQLSERVER_PASSWORD || '123456',
        database: process.env.SQLSERVER_DB || 'NBSTEST',
        encrypt: process.env.SQLSERVER_ENCRYPT === 'true' || false,
        trustServerCertificate: process.env.SQLSERVER_TRUST_CERT === 'true' || true,
        requestTimeout: 30000,
        connectionTimeout: 30000,
        poolMax: 10,
        poolMin: 0,
        idleTimeout: 30000,
        acquireTimeout: 60000
      },
        databaseType: 'sqlserver', 
        logging: {
          level: 'debug',
          enableConsole: true
        },
        security: {
          bcryptRounds: 8 
        }
      },

      production: {
        app: {
          port: process.env.PORT || 8080,
          host: '0.0.0.0'
        },
        database: {
          host: process.env.DB_HOST || '*************',
          port: process.env.DB_PORT || 3306,
          user: process.env.DB_USER || 'root',
          password: process.env.DB_PASSWORD,
          database: process.env.DB_NAME || 'feedback_management'
        },
        sqlserver: {
        server: process.env.SQLSERVER_HOST || 'localhost',
        port: parseInt(process.env.SQLSERVER_PORT) || 1433,
        user: process.env.SQLSERVER_USER || 'sa',
        password: process.env.SQLSERVER_PASSWORD || '123456',
        database: process.env.SQLSERVER_DB || 'NBSTEST',
        encrypt: process.env.SQLSERVER_ENCRYPT === 'true' || false,
        trustServerCertificate: process.env.SQLSERVER_TRUST_CERT === 'true' || true,
        requestTimeout: 30000,
        connectionTimeout: 30000,
        poolMax: 10,
        poolMin: 0,
        idleTimeout: 30000,
        acquireTimeout: 60000
      },
        databaseType: process.env.DATABASE_TYPE || 'mysql',
        session: {
          secret: process.env.SESSION_SECRET || 'production-secret-key',
          cookie: {
            secure: true, // 生产环境使用HTTPS
            maxAge: 12 * 60 * 60 * 1000 // 12小时
          }
        },
        logging: {
          level: 'warn'
        },
        security: {
          bcryptRounds: 12
        }
      },

      test: {
        app: {
          port: 3001
        },
        database: {
          database: 'feedback_management_test'
        },
        logging: {
          level: 'error'
        },
        security: {
          bcryptRounds: 4 // 测试环境使用最少的加密轮数
        }
      }
    };

    return envConfigs[env] || {};
  }

  /**
   * 加载本地配置文件（可选）
   */
  loadLocalConfig() {
    const localConfigPath = path.join(__dirname, 'local.js');
    
    if (fs.existsSync(localConfigPath)) {
      try {
        delete require.cache[require.resolve(localConfigPath)];
        return require(localConfigPath);
      } catch (error) {
        console.warn('加载本地配置文件失败:', error.message);
      }
    }
    
    return {};
  }

  /**
   * 验证配置
   */
  validateConfig() {
    const requiredConfigs = [
      'app.port',
      'database.host',
      'database.user',
      'database.database',
      'session.secret'
    ];

    for (const configPath of requiredConfigs) {
      if (!this.get(configPath)) {
        throw new Error(`缺少必要配置: ${configPath}`);
      }
    }
  }

  /**
   * 获取配置值
   * @param {string} path - 配置路径，如 'database.host'
   * @param {*} defaultValue - 默认值
   * @returns {*} 配置值
   */
  get(path, defaultValue = null) {
    const keys = path.split('.');
    let value = this.configs;

    for (const key of keys) {
      if (value && typeof value === 'object' && key in value) {
        value = value[key];
      } else {
        return defaultValue;
      }
    }

    return value;
  }

  /**
   * 设置配置值
   * @param {string} path - 配置路径
   * @param {*} value - 配置值
   */
  set(path, value) {
    const keys = path.split('.');
    let current = this.configs;

    for (let i = 0; i < keys.length - 1; i++) {
      const key = keys[i];
      if (!current[key] || typeof current[key] !== 'object') {
        current[key] = {};
      }
      current = current[key];
    }

    current[keys[keys.length - 1]] = value;
  }

  /**
   * 获取所有配置
   */
  getAll() {
    return { ...this.configs };
  }

  /**
   * 获取当前环境
   */
  getEnv() {
    return this.env;
  }

  /**
   * 检查是否为开发环境
   */
  isDevelopment() {
    return this.env === 'development';
  }

  /**
   * 检查是否为生产环境
   */
  isProduction() {
    return this.env === 'production';
  }

  /**
   * 检查是否为测试环境
   */
  isTest() {
    return this.env === 'test';
  }

  /**
   * 重新加载配置
   */
  reload() {
    this.loadConfigs();
  }
}

// 创建单例实例
const config = new ConfigManager();

module.exports = config;
