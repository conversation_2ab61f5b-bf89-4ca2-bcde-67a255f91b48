// index.js
// 获取应用实例
const utils = require('../../utils/util');
const app = getApp()
const { userInfo, OpenId } = require('../../config/common')
const user = require('../../request/user')
const RoleChecker = require('../../utils/role-checker')

Page({
    data: {
        userInfo: {},
        currentUserRole: '', // 当前用户角色
        isXianChangDaiBiao: false, // 是否为现场代表
        hasVehicleMaintenancePermission: false, // 是否有车辆维保权限
        hasFeedbackPermission: false, // 是否有现场信息反馈权限
    },
    radioChange(e) {
        let items = this.data.userInfo.Company;
        items.forEach(items => {
            if (items.CompanyId == e.detail.value) {
                items.IsMain = true;
            }
            else {
                items.IsMain = false;
            }
        })
        this.setData({
            'userInfo.Company': items
        })
        utils.setStorage('userInfo', this.data.userInfo)
    },
    AgainLogin() {
        wx.removeStorage({
            key: 'userInfo'
        })

        // 清理全局状态
        const app = getApp();
        app.globalData.isLoggedIn = false;
        app.globalData.userInfo = null;

        this.getStologin();
    },

    // 跳转到车辆维保模块
    goToVehicleMaintenance() {
        // 检查用户权限
        if (!this.checkVehicleMaintenancePermission()) {
            wx.showToast({
                title: '您没有访问车辆维保的权限',
                icon: 'none',
                duration: 2000
            });
            return;
        }

        wx.navigateTo({
            url: '/subpackages/order-management/order/order'
        });
    },

    // 跳转到现场信息反馈模块
    goToFeedback() {
        // 检查是否已登录
        console.log("跳转到现场信息反馈 - 当前用户信息:", this.data.userInfo);

        const hasValidUserId = this.data.userInfo && (this.data.userInfo.PersonId || this.data.userInfo.userId);

        if (!hasValidUserId) {
            wx.showToast({
                title: '请先登录',
                icon: 'none'
            });
            return;
        }

        // 检查用户权限
        if (!this.checkFeedbackPermission()) {
            wx.showToast({
                title: '您没有访问现场信息反馈的权限',
                icon: 'none',
                duration: 2000
            });
            return;
        }

        // 确保用户信息已存储到本地存储中
        try {
            wx.setStorageSync('userInfo', this.data.userInfo);
            console.log("用户信息已同步到本地存储");
        } catch (error) {
            console.error("存储用户信息失败:", error);
        }

        wx.navigateTo({
            url: '/subpackages/feedback-management/fbindex/fbindex'
        });
    },

    // 检查车辆维保访问权限
    checkVehicleMaintenancePermission() {
        if (!this.data.userInfo || !this.data.userInfo.Roles) {
            return false;
        }

        // 检查用户是否有司机角色(9010)或其他有权限的角色
        const hasPermission = this.data.userInfo.Roles.some(role => {
            return role.RoleId === '9010'; // 司机角色
        });

        console.log('车辆维保权限检查:', {
            userRoles: this.data.userInfo.Roles,
            hasPermission: hasPermission
        });

        return hasPermission;
    },

    // 检查现场信息反馈访问权限
    checkFeedbackPermission() {
        if (!this.data.userInfo || !this.data.userInfo.Roles) {
            return false;
        }

        // 检查用户是否有现场代表角色(9060)
        const hasPermission = this.data.userInfo.Roles.some(role => {
            return role.RoleId === '9060'; // 现场代表角色
        });

        console.log('现场信息反馈权限检查:', {
            userRoles: this.data.userInfo.Roles,
            hasPermission: hasPermission
        });

        return hasPermission;
    },
    //将登陆信息绑定到页面
    setUserInfo(res) {
        console.log("设置用户信息:", res);
        this.setData({
            userInfo: res
        });

        // 同步到本地存储
        try {
            wx.setStorageSync('userInfo', res);
            console.log("用户信息已同步到本地存储");

            // 同步到全局状态
            const app = getApp();
            app.globalData.isLoggedIn = true;
            app.globalData.userInfo = res;
            console.log("用户信息已同步到全局状态");
        } catch (error) {
            console.error("存储用户信息失败:", error);
        }

        // 检查用户角色
        this.checkUserRole();
    },

    // 检查用户角色
    checkUserRole() {
        // 检查各种权限
        const hasVehiclePermission = this.checkVehicleMaintenancePermission();
        const hasFeedbackPermission = this.checkFeedbackPermission();

        // 检查是否为现场代表
        RoleChecker.isXianChangDaiBiao().then(() => {
            console.log('当前用户是现场代表');
            this.setData({
                isXianChangDaiBiao: true,
                currentUserRole: '现场代表',
                hasVehicleMaintenancePermission: hasVehiclePermission,
                hasFeedbackPermission: hasFeedbackPermission
            });
        }).catch(() => {
            console.log('当前用户不是现场代表');
            this.setData({
                isXianChangDaiBiao: false,
                hasVehicleMaintenancePermission: hasVehiclePermission,
                hasFeedbackPermission: hasFeedbackPermission
            });

            // 检查其他角色
            this.checkOtherRoles();
        });
    },

    // 检查其他角色
    checkOtherRoles() {
        RoleChecker.getUserMaxRole().then(maxRole => {
            const roleName = RoleChecker.getRoleName(maxRole);
            this.setData({
                currentUserRole: roleName
            });
            console.log('当前用户最高角色:', roleName);
        }).catch(err => {
            console.log('获取用户角色失败:', err);
            this.setData({
                currentUserRole: '未知角色'
            });
        });
    },
    checkSession() {
        console.log('检查微信session状态');

        //先检查登陆是否过期
        wx.checkSession({
            success: () => {
                console.log('微信session有效');

                // 先检查全局登录状态
                const app = getApp();
                if (app.globalData.isLoggedIn && app.globalData.userInfo) {
                    console.log('全局登录状态有效，直接使用');
                    this.setUserInfo(app.globalData.userInfo);
                    return;
                }

                // 全局状态无效，检查本地存储
                this.getStologin();
            },
            fail: () => {
                console.log('微信session已过期，清理数据并跳转到登录页');
                wx.removeStorage({
                    key: 'OpenId'
                })
                wx.removeStorage({
                    key: 'userInfo'
                })

                // 清理全局状态
                const app = getApp();
                app.globalData.isLoggedIn = false;
                app.globalData.userInfo = null;

                // 直接跳转到登录页
                wx.redirectTo({
                    url: '../Login/Login',
                })
            }
        })
    },
    //code换openid
    userLogin() {
        utils.wxLogin().then(
            res => {
                user.login(res);
            }
        )
    },

    getStologin() {
        utils.getStorage('userInfo').then(res => {
            this.setUserInfo(res);
        }).catch(err => {
            console.log("本地userInfo获取失败，检查全局状态");

            // 先检查全局登录状态
            const app = getApp();
            if (app.globalData.isLoggedIn && app.globalData.userInfo) {
                console.log("使用全局用户信息");
                this.setUserInfo(app.globalData.userInfo);
                return;
            }

            // 没有用户信息，直接跳转到登录页
            console.log("没有用户信息，直接跳转到登录页");
            wx.redirectTo({
                url: '../Login/Login',
            })
        })
    },
    onLoad() {
        console.log('=== index页面加载 - 开始检查登录状态 ===');

        // 调试信息：显示当前存储状态
        const localUserInfo = wx.getStorageSync('userInfo');
        const localOpenId = wx.getStorageSync('OpenId');
        console.log('本地存储状态:', {
            hasUserInfo: !!localUserInfo,
            hasOpenId: !!localOpenId,
            userInfo: localUserInfo
        });

        // 首先检查全局登录状态
        const app = getApp();
        console.log('全局登录状态:', {
            isLoggedIn: app.globalData.isLoggedIn,
            hasUserInfo: !!app.globalData.userInfo,
            userInfo: app.globalData.userInfo
        });

        if (app.globalData.isLoggedIn && app.globalData.userInfo) {
            console.log('使用全局登录状态，直接设置用户信息');
            this.setUserInfo(app.globalData.userInfo);
        } else {
            console.log('全局登录状态无效，执行本地检查');
            this.checkSession();
        }
    }
})
